"""
Generic statement conversion prompt for Oracle to PostgreSQL migration.
No hardcoded scenarios - purely generic and flexible approach.
"""
from typing import Dict

def create_generic_statement_conversion_prompt(source_context: Dict, target_error_context: Dict, error_message: str, target_statements: list = None, previous_feedback: str = None) -> str:
    """
    Creates a generic, flexible prompt for Oracle to PostgreSQL statement conversion.
    
    This approach is:
    - Generic: No hardcoded error scenarios
    - Flexible: Handles any type of Oracle to PostgreSQL migration issue
    - Focused: Clear, concise instructions
    - Systematic: Structured analysis approach
    """
    
    # Check if this is a target database-specific scenario
    is_target_specific = (source_context.error_statement_number == 0 or
                         not source_context.error_statement or
                         source_context.error_statement.strip() == "")
    
    # Add feedback section if provided
    feedback_section = ""
    if previous_feedback:
        feedback_section = f"""
## 🔄 PREVIOUS ATTEMPT FEEDBACK
{previous_feedback}

**Required Improvements:**
- Address the specific issues mentioned above
- Apply more comprehensive analysis
- Ensure complete error resolution across the entire statement
"""

    if is_target_specific:
        return f"""# PostgreSQL Database Expert - Generic Error Resolution

## 🎯 OBJECTIVE
Fix the PostgreSQL error using systematic database expertise.

{feedback_section}

## 📊 ERROR CONTEXT
**Error Message:** {error_message}

**Error Statement (#{target_error_context.error_statement_number}):**
```sql
{target_error_context.error_statement}
```

**Context Before (#{target_error_context.before_statement_number}):**
```sql
{target_error_context.before_statement}
```

**Context After (#{target_error_context.after_statement_number}):**
```sql
{target_error_context.after_statement}
```

## 🔍 SYSTEMATIC ANALYSIS APPROACH

### 1. Error Understanding
- Analyze the error message to understand what PostgreSQL is rejecting
- Identify the specific syntax, function, or construct causing the issue
- Determine the scope of the problem within the statement

### 2. Comprehensive Pattern Detection
- **CRITICAL**: Scan the ENTIRE statement for ALL instances of the same issue
- Count how many times the problematic pattern appears
- Map the location of each occurrence within the statement

### 3. PostgreSQL Solution Application
- Apply PostgreSQL syntax rules and best practices
- Use database-native constructs that achieve the same functionality
- Ensure the solution maintains the original business logic

### 4. Complete Resolution Verification
- Fix ALL instances of the identified issue within the statement
- Verify no partial fixes or missed occurrences remain
- Ensure the entire statement is PostgreSQL-compliant

## 📤 OUTPUT FORMAT
```json
{{
  "corrected_statements": [
    {{
      "statement_number": {target_error_context.error_statement_number},
      "original_statement": "<original statement>",
      "corrected_statement": "<corrected PostgreSQL statement>",
      "statement_type": "error_statement",
      "changes_made": "<description of all changes applied>"
    }}
  ],
  "explanation": "<systematic analysis of the error and comprehensive solution rationale>"
}}
```

## ⚠️ CRITICAL REQUIREMENTS
- Fix ONLY the error statement (#{target_error_context.error_statement_number})
- Identify and fix ALL instances of the same issue within the statement
- Provide executable PostgreSQL code, not comments or placeholders
- Maintain original functionality and business logic
"""

    else:
        return f"""# Oracle to PostgreSQL Migration Expert - Generic Conversion

## 🎯 OBJECTIVE
Convert the Oracle statement to correct PostgreSQL syntax, resolving the deployment error.

{feedback_section}

## 📊 MIGRATION CONTEXT
**Error Message:** {error_message}

**Oracle Source (#{source_context.error_statement_number}):**
```sql
{source_context.error_statement}
```

**PostgreSQL Target with Error (#{target_error_context.error_statement_number}):**
```sql
{target_error_context.error_statement}
```

**Context Before (#{target_error_context.before_statement_number}):**
```sql
{target_error_context.before_statement}
```

**Context After (#{target_error_context.after_statement_number}):**
```sql
{target_error_context.after_statement}
```

## 🔍 SYSTEMATIC MIGRATION APPROACH

### 1. Error Analysis
- Understand what PostgreSQL is rejecting in the current statement
- Compare with the Oracle source to understand the intended functionality
- Identify the specific Oracle construct that needs PostgreSQL conversion

### 2. Comprehensive Pattern Identification
- **CRITICAL**: Scan the ENTIRE statement for ALL instances of the same conversion issue
- Count how many times the problematic Oracle pattern appears
- Map each occurrence location within the statement structure

### 3. Oracle-to-PostgreSQL Conversion Strategy
- Research the appropriate PostgreSQL equivalent for the Oracle construct
- Design a conversion approach that preserves the original business logic
- Apply PostgreSQL syntax rules and best practices

### 4. Complete Conversion Application
- Convert ALL instances of the identified Oracle pattern within the statement
- Apply consistent PostgreSQL syntax across all occurrences
- Verify no partial conversions or Oracle remnants remain

## 🛠️ GENERIC MIGRATION PRINCIPLES

### Universal Conversion Rules:
- **Syntax Differences**: Apply PostgreSQL syntax rules for any construct
- **Function Mapping**: Replace Oracle functions with PostgreSQL equivalents
- **Data Type Conversion**: Use appropriate PostgreSQL data types and casting
- **Clause Ordering**: Follow PostgreSQL requirements for SQL clause sequence
- **Operator Compatibility**: Use PostgreSQL-supported operators and expressions

### Systematic Approach:
- **Identify**: What Oracle feature is causing the PostgreSQL error?
- **Research**: What is the PostgreSQL way to achieve the same result?
- **Convert**: Apply the PostgreSQL approach consistently
- **Verify**: Ensure all instances are converted and functionality is preserved

## 📤 OUTPUT FORMAT
```json
{{
  "corrected_statements": [
    {{
      "statement_number": {target_error_context.error_statement_number},
      "original_statement": "<original PostgreSQL statement>",
      "corrected_statement": "<corrected PostgreSQL statement>",
      "statement_type": "error_statement",
      "changes_made": "<description of Oracle→PostgreSQL conversions applied>"
    }}
  ],
  "explanation": "<systematic migration analysis and conversion rationale>"
}}
```

## ⚠️ CRITICAL REQUIREMENTS
- Fix ONLY the error statement (#{target_error_context.error_statement_number})
- Identify and convert ALL instances of Oracle patterns within the statement
- Apply Oracle→PostgreSQL migration expertise systematically
- Preserve original business logic and functionality
- Provide executable PostgreSQL code, not comments or placeholders
- Handle complex statements with multiple sub-queries or nested constructs comprehensively
"""
