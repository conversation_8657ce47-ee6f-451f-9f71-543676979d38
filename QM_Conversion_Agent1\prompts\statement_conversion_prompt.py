"""
Prompts for statement conversion in database conversion.
"""
from typing import Dict

def create_statement_conversion_prompt(source_context: Dict, target_error_context: Dict, error_message: str, target_statements: list = None, previous_feedback: str = None) -> str:
    """
    Creates a prompt for converting the error statement.

    This function creates a prompt that instructs the LLM to convert the error statement
    from the target context to fix the error, using the source context as a reference.

    Args:
        source_context: Dictionary containing the source context (before, error, after statements)
        target_error_context: Dictionary containing the target error context
        error_message: The error message from deployment

    Returns:
        A formatted prompt string for the LLM
    """
    # Check if this is a target database-specific scenario (no source mapping)
    is_target_specific = (source_context.error_statement_number == 0 or
                         not source_context.error_statement or
                         source_context.error_statement.strip() == "")

    # Add feedback section if provided
    feedback_section = ""
    if previous_feedback:
        feedback_section = f"""
PREVIOUS CONVERSION FEEDBACK:
The previous conversion attempt was rejected with this feedback:
{previous_feedback}

Please address these specific conversion issues:
- Fix the exact problems mentioned in the feedback
- Ensure proper target database syntax and data types
- {"Apply target database expertise directly" if is_target_specific else "Verify functional equivalence with the source"}
- Pay attention to target database-specific requirements
- Provide more detailed analysis of the conversion changes

"""

    if is_target_specific:
        # Target database-specific conversion prompt (no source dependency)
        return f"""🚨🚨🚨 CRITICAL NAMING PRESERVATION RULE 🚨🚨🚨
PRESERVE ALL ORIGINAL NAMES EXACTLY AS THEY APPEAR - NO MODIFICATIONS ALLOWED
- Keep ALL function/procedure/label names EXACTLY as found in the source code
- Do NOT add any prefixes, suffixes, or modify the original naming pattern
- Do NOT convert to different naming conventions (snake_case, camelCase, etc.)
- Use the EXACT same identifiers throughout the entire conversion process

🚨🚨🚨 NO TEMPLATES OR PLACEHOLDERS ALLOWED 🚨🚨🚨
PROVIDE ONLY EXECUTABLE CODE - NO COMMENTS, TEMPLATES, OR PLACEHOLDERS
- Do NOT use comments as solutions or implementations
- Do NOT provide template code with placeholder values
- Do NOT add explanatory comments in place of actual code
- ALWAYS provide complete working database operations
- Use REAL table names, column names, and conditions from the actual code

You are a Target Database Expert with deep expertise in target database systems. Your task is to fix the error in the target database statement using target database expertise directly.

TARGET DATABASE-SPECIFIC CONVERSION DETECTED:
This statement appears to be target database-specific with no source equivalent. Apply target database expertise directly to resolve the syntax error.

{feedback_section}

ERROR MESSAGE:
{error_message}

COMPLETE TARGET CODE CONTEXT:
{f"Complete target code available for analysis - examine the full structure to understand function signatures, parameter types, and return patterns" if target_statements else "Limited context available"}

{f'''
FULL TARGET STATEMENTS:
{chr(10).join([f"{i+1}. {stmt.strip()}" for i, stmt in enumerate(target_statements)])}
''' if target_statements else ""}

TARGET ERROR CONTEXT (Target Database with error):
Before Error (#{target_error_context.before_statement_number}):
{target_error_context.before_statement}

Error Statement (#{target_error_context.error_statement_number}):
{target_error_context.error_statement}

After Error (#{target_error_context.after_statement_number}):
{target_error_context.after_statement}

COMPREHENSIVE TARGET DATABASE APPROACH:
Handle ANY type of target database issue using your database expertise.

STEP 1: ERROR ANALYSIS
- What specific rule or syntax is being violated according to the error message?
- What does the error message explicitly tell you needs to be changed?
- What type of database construct is this (function, procedure, query, etc.)?

STEP 2: CONTEXT ANALYSIS
- Examine the complete target code structure to understand:
  * Function signatures, parameter types, and return patterns
  * Database object type and requirements
  * Surrounding elements that provide context
  * Expected behavior based on the complete structure

STEP 3: TARGET DATABASE EXPERTISE
- Apply your deep target database knowledge to fix the issue
- Use target database-native patterns and syntax
- Handle ANY database operation type including:
  * Function/procedure definitions and body syntax
  * Data types, casting, and parameter handling
  * Control flow, loops, and conditional statements
  * Error handling and exception management
  * Database-specific constructs and features

STEP 4: COMPREHENSIVE FIX
- Fix the specific error while maintaining functionality
- Apply target database best practices
- Ensure proper syntax and semantic correctness
- Handle complex scenarios requiring multiple changes
- **CRITICAL: Scan the ENTIRE statement for ALL instances of the same error pattern**
- **Fix ALL occurrences of the same issue within the statement, not just the first one**

TASK:
Use your target database expertise to fix ANY type of issue in the target database code. Handle simple syntax fixes, complex logic corrections, data type issues, function signature problems, or any other database-specific challenges.

OUTPUT FORMAT (JSON):
{{
  "corrected_statements": [
    {{
      "statement_number": <integer>,
      "original_statement": "<original target statement>",
      "corrected_statement": "<corrected target statement>",
      "statement_type": "before_error|error_statement|after_error",
      "changes_made": "<detailed description of specific changes made including: 1) What was wrong in the original statement, 2) Specific syntax transformations applied, 3) Why these changes were necessary, 4) How the changes resolve the error, 5) Target database expertise applied>"
    }},
    ...
  ],
  "explanation": "<comprehensive target database-specific analysis including: 1) Error message interpretation, 2) Target database syntax requirements, 3) Target database expertise applied, 4) Fix rationale, 5) Target database best practices used, 6) Validation that the fix resolves the reported error>"
}}

IMPORTANT:
- This is a target database-specific conversion - no source reference needed
- Apply target database expertise directly
- Focus on target database syntax and best practices
- Return ALL statements in the corrected_statements array"""

    else:
        # Standard conversion prompt (with source reference)
        return f"""🚨🚨🚨 CRITICAL NAMING PRESERVATION RULE 🚨🚨🚨
PRESERVE ALL ORIGINAL NAMES EXACTLY AS THEY APPEAR - NO MODIFICATIONS ALLOWED
- Keep ALL function/procedure/label names EXACTLY as found in the source code
- Do NOT add any prefixes, suffixes, or modify the original naming pattern
- Do NOT convert to different naming conventions (snake_case, camelCase, etc.)
- Use the EXACT same identifiers throughout the entire conversion process

🚨🚨🚨 NO TEMPLATES OR PLACEHOLDERS ALLOWED 🚨🚨🚨
PROVIDE ONLY EXECUTABLE CODE - NO COMMENTS, TEMPLATES, OR PLACEHOLDERS
- Do NOT use comments as solutions or implementations
- Do NOT provide template code with placeholder values
- Do NOT add explanatory comments in place of actual code
- ALWAYS provide complete working database operations
- Use REAL table names, column names, and conditions from the actual code

You are an Oracle to PostgreSQL Database Migration Expert with deep expertise in both Oracle and PostgreSQL database systems. Your task is to fix the error in the target PostgreSQL statement by converting it correctly from the Oracle source.

{feedback_section}

ERROR MESSAGE:
{error_message}

ADDITIONAL CONTEXT:
{f"Complete target code context available for analysis - examine the full structure to understand function signatures, parameter types, and return patterns" if target_statements else "Limited context available"}

SOURCE CONTEXT (Oracle):
Before Error (#{source_context.before_statement_number}):
{source_context.before_statement}

Error Statement (#{source_context.error_statement_number}):
{source_context.error_statement}

After Error (#{source_context.after_statement_number}):
{source_context.after_statement}

TARGET CONTEXT (PostgreSQL with error):
Before Error (#{target_error_context.before_statement_number}):
{target_error_context.before_statement}

Error Statement (#{target_error_context.error_statement_number}):
{target_error_context.error_statement}

After Error (#{target_error_context.after_statement_number}):
{target_error_context.after_statement}

ANALYTICAL APPROACH:
Follow this systematic error-resolution methodology:

STEP 1: ERROR MESSAGE ANALYSIS
- What specific rule or syntax is being violated according to the error message?
- What does the error message explicitly tell you needs to be changed?
- What hints or suggestions does the error provide?

STEP 2: CONTEXT ANALYSIS
- What type of database object/statement is this (function, procedure, query, etc.)?
- What are the surrounding elements that provide context?
- If this is within a function, examine the complete function structure to understand:
  * Parameter definitions (IN, OUT, INOUT types)
  * Return type declarations
  * Function signature patterns
  * Expected behavior based on the function definition

STEP 3: TARGET DATABASE REQUIREMENTS
- What are the syntax rules for this type of statement in the target database?
- What are the correct patterns for this specific database operation?
- How should this type of construct be properly implemented in the target database?
- What are the target database's preferred methods for achieving this business outcome?
- Are there target database-specific patterns that accomplish the same result?
- Consider that some statements may be target database-specific constructs with no source equivalent

STEP 4: ERROR-DRIVEN FIX APPLICATION
- Apply the specific fix that directly addresses the error message
- Ensure the solution follows target database syntax rules
- Preserve the original business logic and functionality
- **CRITICAL: Scan the ENTIRE statement for ALL instances of the same error pattern**
- **Fix ALL occurrences of the same issue within the statement, not just the first one**
- **Example: If the same syntax error exists multiple times, fix ALL instances**

STEP 5: CROSS-DATABASE IMPLEMENTATION ANALYSIS
- How does the target database typically implement this type of functionality?
- What are the target database's standard patterns for this business outcome?
- Are there target database-specific approaches that achieve the same result?
- Focus on target database best practices for this type of operation

STEP 6: ERROR-DRIVEN SOLUTION ANALYSIS
- What does the error message specifically prohibit or disallow?
- What alternative approaches does the target database support for the same outcome?
- If error mentions "cannot have parameter", consider removing parameters entirely
- If error mentions "not allowed", explore completely different target database patterns
- Focus on what the target database DOES allow rather than what it prohibits

STEP 7: UNIVERSAL UNSUPPORTED FEATURE CONVERSION STRATEGY
When encountering ANY feature not supported in PostgreSQL, apply this intelligent conversion approach:

**DYNAMIC ANALYSIS FRAMEWORK:**
1. **Identify the Unsupported Feature**: What specific syntax/function/construct is causing the error?
2. **Understand the Business Purpose**: What is this feature trying to accomplish?
3. **Find PostgreSQL Equivalents**: What PostgreSQL features can achieve the same outcome?
4. **Implement Alternative**: Create a working PostgreSQL solution that preserves functionality

**UNIVERSAL CONVERSION PATTERNS:**
- **Unsupported Functions**: Replace with PostgreSQL equivalent functions or custom implementations
- **Unsupported Syntax**: Rewrite using PostgreSQL-compatible syntax patterns
- **Unsupported Data Types**: Convert to PostgreSQL-supported data types with proper casting
- **Unsupported Control Structures**: Replace with PostgreSQL control flow constructs
- **Unsupported Operators**: Use PostgreSQL-compatible operators or function equivalents
- **Unsupported Keywords**: Replace with PostgreSQL-supported alternatives

**INTELLIGENT REPLACEMENT STRATEGY:**
1. **Analyze Error Context**: What does the error message tell us about what's not supported?
2. **Research PostgreSQL Alternatives**: What PostgreSQL features provide similar functionality?
3. **Preserve Business Logic**: Ensure the replacement achieves the same business outcome
4. **Validate Syntax**: Confirm the replacement follows PostgreSQL syntax rules
5. **Test Functionality**: Verify the alternative maintains the original behavior

**ADAPTIVE IMPLEMENTATION APPROACH:**
- **For Control Flow Issues**: Research and apply appropriate PostgreSQL control structures
- **For Function Issues**: Find PostgreSQL equivalent functions or create custom logic implementations
- **For Data Type Issues**: Use appropriate PostgreSQL data types with proper conversion methods
- **For Syntax Issues**: Rewrite using PostgreSQL-compatible syntax patterns and constructs
- **For Operator Issues**: Use PostgreSQL-supported operators or equivalent function alternatives
- **For Any Other Issues**: Apply PostgreSQL expertise to find the most suitable alternative approach

ORACLE TO POSTGRESQL EXPERT KNOWLEDGE:
Apply your deep Oracle→PostgreSQL migration expertise to understand common pattern differences:

EXPERT PATTERN RECOGNITION:
- Oracle procedures vs PostgreSQL functions handle return values differently
- Oracle OUT parameters vs PostgreSQL OUT parameters may use different syntax patterns
- Oracle assignment patterns vs PostgreSQL assignment patterns may vary
- Oracle return mechanisms vs PostgreSQL return mechanisms often differ
- Oracle data types vs PostgreSQL data types require different casting approaches
- Oracle function calls vs PostgreSQL function calls may have different parameter handling

EXPERT SOLUTION APPROACH:
- When Oracle uses one approach, PostgreSQL may require a completely different approach
- The same business outcome can be achieved through different database-specific methods
- Focus on PostgreSQL-native patterns rather than direct Oracle translations
- Consider PostgreSQL best practices for the specific type of operation being performed

🚨 **CRITICAL NAMING CONSISTENCY MANDATE**:
**PRESERVE ALL ORIGINAL NAMES EXACTLY AS THEY APPEAR IN THE SOURCE/TARGET CODE**
- Do NOT modify function names, procedure names, label names, or any identifiers
- Keep the EXACT same naming pattern and style throughout all conversions
- Maintain consistency across the entire codebase migration

TASK:
1. **Analyze the error message** as your primary guide for what needs to be fixed
2. **Examine the complete context** to understand the database object type and requirements
3. **Identify unsupported features** causing the error (syntax, functions, operators, etc.)
4. **Apply universal conversion strategy** to find PostgreSQL alternatives for ANY unsupported feature
5. **For statements with source equivalents**: Use source context as reference for business logic
6. **For target database-specific statements**: Apply target database expertise directly
7. **🚨 PRESERVE ORIGINAL NAMING**: Keep all function/procedure/label names EXACTLY as found in the code
8. **Create complete alternative implementations** that achieve the same business outcome
9. **🚨 SCAN FOR MULTIPLE OCCURRENCES**: Thoroughly examine the ENTIRE error statement for ALL instances of the same error pattern
10. **🚨 FIX ALL INSTANCES**: Fix ALL occurrences of the same issue within the statement, not just the first one found
11. **🚨 SYNTAX ORDER ANALYSIS**: Analyze the error message to identify any syntax ordering issues in the statement
12. **🚨 MULTIPLE QUERY STATEMENTS**: If the error statement contains multiple queries, examine and fix ALL of them in one iteration
13. **🚨 COMPLETE TEXT SCANNING**: Search through the ENTIRE error statement text for ALL instances of the problematic pattern
14. **🚨 MANDATORY COMPREHENSIVE FIX**: Fix EVERY single occurrence of the identified issue within the statement - do not leave any unfixed
15. **🚨 STEP-BY-STEP PATTERN FIX**:
    a) Identify the specific syntax pattern causing the error
    b) Count total occurrences of this pattern in the statement
    c) Apply the correct PostgreSQL syntax for each occurrence
    d) Repeat for ALL instances - do not stop after fixing the first one
16. **Apply the fix** that directly resolves the reported error with proper PostgreSQL syntax
17. **CRITICAL: Fix ONLY the ERROR STATEMENT - before/after are for context only**
   - **PRIMARY FOCUS**: Fix the deployment error in the error statement ONLY
   - **CONTEXT ONLY**: Before/after statements are provided for business logic understanding
   - **DO NOT MODIFY**: Before/after statements unless they are also causing the specific error
   - Ensure the error statement solution follows PostgreSQL syntax rules
10. **Validate that business logic and functionality are preserved** through the alternative implementation

**UNIVERSAL CONVERSION MANDATE:**
- **NEVER just remove unsupported features** without providing working alternatives
- **NEVER use comments as solutions** - comments do not execute business logic
- **NEVER comment out problematic code** - this breaks functionality
- **ALWAYS provide complete EXECUTABLE implementations** that maintain the original functionality
- **ALWAYS provide WORKING PostgreSQL code** that performs the same business operation
- **ANALYZE the business purpose** of any unsupported feature before converting
- **USE PostgreSQL best practices** to achieve the same business outcomes
- **ENSURE the solution works** for the specific error scenario encountered
- **CRITICAL: The corrected statement must be EXECUTABLE PostgreSQL code, not comments**
- **🚨 MULTIPLE OCCURRENCE RULE**: Scan the ENTIRE statement for ALL instances of the same error pattern and fix ALL of them
- **🚨 COMPREHENSIVE FIXING**: Don't just fix the first occurrence - fix every instance of the same issue within the statement
- **🚨 SYNTAX PATTERN ANALYSIS**: Identify and understand the specific syntax pattern causing the error
- **🚨 MULTIPLE QUERY STATEMENTS**: If the error statement contains multiple queries, fix ALL of them in one iteration
- **🚨 EXAMPLE COMPREHENSIVE FIX**: If statement contains multiple instances of the same syntax error, fix ALL instances consistently
- **🚨 NO PARTIAL SOLUTIONS**: Never fix only some instances while leaving others broken - fix ALL occurrences in the same statement
- **🚨 CRITICAL PATTERN SEARCH**: Use text search to find ALL instances of the problematic pattern in the ENTIRE statement text
- **🚨 MANDATORY COMPLETE SCAN**: Count how many times the error pattern appears and fix EVERY single instance

CONVERSION GUIDELINES:
- **CRITICAL**: When encountering ANY unsupported PostgreSQL feature, provide COMPLETE alternative implementations
- Fix ONLY what's necessary to resolve the error
- Maintain the same business logic and functionality
- Follow PostgreSQL syntax and best practices

**UNIVERSAL IMPLEMENTATION RULES:**
- **ALWAYS provide a complete working alternative** that maintains business logic
- **NEVER just remove unsupported features** without providing equivalent functionality
- **NEVER use comments, explanations, or documentation as the solution**
- **ANALYZE the business purpose** of the unsupported feature to understand what it accomplishes
- **CHOOSE the most appropriate PostgreSQL pattern** based on the specific context and requirements
- **RESEARCH PostgreSQL alternatives** that can achieve the same functional outcome
- **IMPLEMENT solutions** using PostgreSQL-native constructs and best practices

**WHAT CONSTITUTES A PROPER FIX:**
✅ **GOOD**: Actual database operations using real table/column names from the code
✅ **GOOD**: Concrete business logic implementation that performs the intended operation
✅ **GOOD**: Working PostgreSQL code that executes the same business function
❌ **BAD**: Comments, explanations, or documentation without executable code
❌ **BAD**: Generic templates with placeholder names or conditions
❌ **BAD**: Sample code that doesn't use the actual elements from the source code
❌ **BAD**: Function calls to non-existent or undefined functions
❌ **BAD**: Conditional statements with placeholder conditions that don't exist

**CRITICAL: ANALYZE THE ACTUAL CODE TO UNDERSTAND WHAT THE STATEMENT DOES**
- Look at the complete target code to understand the business logic
- Find what the referenced labels/functions actually contain
- Implement the ACTUAL operations, not generic templates
- Use REAL table names, column names, and conditions from the code

**CODE ANALYSIS METHODOLOGY:**
1. **Examine the target statements list** to understand the complete context
2. **Locate referenced labels/functions** in the target code to see their actual implementation
3. **Extract the real business logic** from the referenced code sections
4. **Implement the actual operations** using the real table names, columns, and logic
5. **Avoid placeholders** - use the actual code elements you find in the analysis
6. **MAINTAIN CONSISTENT NAMING** - use the same naming pattern throughout the conversion
7. **PRESERVE ORIGINAL NAMES** - keep the same function/label names as found in the source code

**UNIVERSAL ANALYSIS APPROACH:**
- **For any control flow statement**: Analyze what operations it references or leads to
- **For any function/procedure call**: Examine the actual implementation in the code
- **For any reference**: Search the complete code context to find the referenced elements
- **For any operation**: Extract the real business logic and data operations involved
- **Always implement the ACTUAL operations** found through code analysis, not generic templates

**🚨 CRITICAL NAMING CONSISTENCY REQUIREMENTS:**
- **MANDATORY**: Use EXACT same names as found in the source/target code
- **MANDATORY**: Do NOT modify naming conventions (keep original case, format, style)
- **MANDATORY**: Maintain consistency across all conversions in the same codebase
- **MANDATORY**: Preserve original identifiers without changing them to different naming styles
- **MANDATORY**: Never convert names to different naming conventions (camelCase, snake_case, etc.)
- **MANDATORY**: Keep function/procedure/label names EXACTLY as they appear in the original code
- **MANDATORY**: Do not add prefixes, suffixes, or modify the original naming pattern

Handle ANY database operation type including but not limited to:
  * Data Manipulation: INSERT, UPDATE, DELETE, SELECT, MERGE, UPSERT
  * Data Definition: CREATE, ALTER, DROP, TRUNCATE, RENAME
  * Transaction Control: COMMIT, ROLLBACK, SAVEPOINT, BEGIN
  * Procedural Operations: DECLARE, SET, CALL, EXECUTE, RETURN
  * Control Flow: IF/ELSE, CASE/WHEN, LOOP, WHILE, FOR, EXCEPTION handling
  * Cursor Operations: OPEN, FETCH, CLOSE cursor statements
  * Aggregate Operations: GROUP BY, HAVING, window functions, analytical functions
  * Join Operations: INNER/OUTER/CROSS joins, subqueries, CTEs
  * Index Operations: CREATE/DROP INDEX, hints, optimization directives
  * Security Operations: GRANT, REVOKE, user/role management
  * System Operations: sequence generation, trigger definitions, view creation

SYNTAX CONVERSION CONSIDERATIONS:
- **Database-specific functions**: Research PostgreSQL equivalents for any Oracle-specific functions
- **Data processing functions**: Find PostgreSQL alternatives for XML/JSON, string, mathematical operations
- **Schema and naming**: Apply PostgreSQL naming conventions and case sensitivity rules
- **Function call syntax**: Adapt parameter order, optional parameters to PostgreSQL patterns
- **Data type handling**: Convert to appropriate PostgreSQL data types with proper casting
- **Sequence and identity**: Use PostgreSQL sequence generation methods
- **Exception handling**: Apply PostgreSQL exception handling syntax patterns
- **Cursor operations**: Adapt to PostgreSQL cursor syntax and lifecycle management
- **Transaction control**: Use PostgreSQL transaction isolation and locking mechanisms
- **Procedural constructs**: Apply PostgreSQL stored procedure/function declaration syntax
- **Event handling**: Convert to PostgreSQL trigger syntax and event patterns
- **Performance optimization**: Use PostgreSQL index creation and optimization approaches
- **Focus on functional equivalence and business logic preservation**

OUTPUT FORMAT (JSON):
{{
  "corrected_statements": [
    {{
      "statement_number": <integer>,
      "original_statement": "<original target statement>",
      "corrected_statement": "<corrected target statement>",
      "statement_type": "before_error|error_statement|after_error",
      "changes_made": "<detailed description of specific changes made including: 1) What was wrong in the original statement, 2) Specific syntax or function transformations applied, 3) Why these changes were necessary, 4) How the changes resolve the error, 5) Any alternative approaches considered>"
    }},
    ...
  ],
  "explanation": "<comprehensive universal conversion analysis including: 1) Error message interpretation - what specific feature/syntax is unsupported, 2) Unsupported feature analysis - what business purpose does the unsupported feature serve, 3) PostgreSQL alternative research - what PostgreSQL features can achieve the same outcome, 4) Implementation strategy - how the alternative solution maintains the original functionality, 5) Business logic preservation - verification that the same business outcome is achieved, 6) Syntax validation - confirmation that the solution follows PostgreSQL rules, 7) Alternative approaches considered and why this solution was chosen, 8) Validation that the fix directly resolves the reported error for ANY type of unsupported feature>"
}}

IMPORTANT:
- Return ALL three statements (before, error, after) in the corrected_statements array
- Use the EXACT statement numbers provided in the context:
  * Before Error: statement_number = {target_error_context.before_statement_number}
  * Error Statement: statement_number = {target_error_context.error_statement_number}
  * After Error: statement_number = {target_error_context.after_statement_number}
- **PRIMARY FOCUS: Fix ONLY the deployment error in the error statement**
- **CONTEXT STATEMENTS: Before/after statements are for context awareness only**
- **DO NOT MODIFY: Before/after statements - keep them exactly as original_statement**
- Only the error_statement correction will be applied to the deployed code
- Before/after statements provide business logic context but should remain unchanged
- For before/after statements: ALWAYS set corrected_statement equal to original_statement
- For error statement: Provide the complete alternative implementation that fixes the error
- Focus on fixing the specific deployment error while maintaining the same functionality
- Be precise and provide complete working solutions for unsupported features"""
