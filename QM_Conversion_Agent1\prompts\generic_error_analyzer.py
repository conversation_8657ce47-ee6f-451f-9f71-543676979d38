"""
Generic error analyzer for Oracle to PostgreSQL migration.
No hardcoded scenarios - purely generic pattern detection.
"""
import re
from typing import Dict, List

class GenericErrorAnalyzer:
    """
    Generic analyzer that detects patterns without hardcoding specific scenarios.
    Focuses on structural analysis rather than specific error types.
    """
    
    def __init__(self):
        # Generic pattern detection - no specific scenarios
        self.analysis_methods = [
            self._analyze_statement_structure,
            self._analyze_keyword_patterns,
            self._analyze_function_patterns,
            self._analyze_syntax_patterns
        ]
    
    def analyze_error_generically(self, error_message: str, statement: str) -> Dict:
        """
        Perform generic error analysis without hardcoded scenarios.
        
        Args:
            error_message: The PostgreSQL error message
            statement: The SQL statement causing the error
            
        Returns:
            Dictionary with generic analysis results
        """
        analysis = {
            'error_keywords': self._extract_error_keywords(error_message),
            'statement_complexity': self._assess_statement_complexity(statement),
            'potential_patterns': [],
            'pattern_counts': {},
            'analysis_suggestions': []
        }
        
        # Apply all generic analysis methods
        for method in self.analysis_methods:
            method_result = method(error_message, statement)
            analysis.update(method_result)
        
        # Generate generic suggestions
        analysis['analysis_suggestions'] = self._generate_generic_suggestions(analysis)
        
        return analysis
    
    def _extract_error_keywords(self, error_message: str) -> List[str]:
        """Extract key terms from error message without scenario assumptions."""
        # Common PostgreSQL error keywords
        error_keywords = []
        
        # Extract quoted terms (often the problematic syntax)
        quoted_terms = re.findall(r'"([^"]+)"', error_message)
        error_keywords.extend(quoted_terms)
        
        # Extract common error indicators
        error_indicators = ['syntax error', 'does not exist', 'cannot cast', 'invalid', 'unexpected']
        for indicator in error_indicators:
            if indicator.lower() in error_message.lower():
                error_keywords.append(indicator)
        
        return list(set(error_keywords))
    
    def _assess_statement_complexity(self, statement: str) -> Dict:
        """Assess statement complexity generically."""
        return {
            'line_count': len(statement.split('\n')),
            'character_count': len(statement),
            'has_subqueries': 'SELECT' in statement.upper() and statement.upper().count('SELECT') > 1,
            'has_multiple_statements': statement.count(';') > 1,
            'has_nested_constructs': any(keyword in statement.upper() for keyword in ['CASE', 'IF', 'LOOP', 'BEGIN'])
        }
    
    def _analyze_statement_structure(self, error_message: str, statement: str) -> Dict:
        """Analyze statement structure generically."""
        structure_analysis = {}
        
        # Count SQL clauses
        sql_clauses = ['SELECT', 'FROM', 'WHERE', 'GROUP BY', 'HAVING', 'ORDER BY', 'INSERT', 'UPDATE', 'DELETE']
        clause_counts = {}
        
        for clause in sql_clauses:
            count = len(re.findall(rf'\b{clause}\b', statement, re.IGNORECASE))
            if count > 0:
                clause_counts[clause.lower()] = count
        
        structure_analysis['clause_counts'] = clause_counts
        structure_analysis['total_clauses'] = sum(clause_counts.values())
        
        return structure_analysis
    
    def _analyze_keyword_patterns(self, error_message: str, statement: str) -> Dict:
        """Analyze keyword patterns generically."""
        keyword_analysis = {}
        
        # Find repeated keywords that might indicate patterns
        words = re.findall(r'\b\w+\b', statement.upper())
        word_counts = {}
        
        for word in words:
            if len(word) > 2:  # Ignore very short words
                word_counts[word] = word_counts.get(word, 0) + 1
        
        # Find words that appear multiple times (potential patterns)
        repeated_keywords = {word: count for word, count in word_counts.items() if count > 1}
        
        keyword_analysis['repeated_keywords'] = repeated_keywords
        keyword_analysis['most_frequent'] = max(repeated_keywords.items(), key=lambda x: x[1]) if repeated_keywords else None
        
        return keyword_analysis
    
    def _analyze_function_patterns(self, error_message: str, statement: str) -> Dict:
        """Analyze function usage patterns generically."""
        function_analysis = {}
        
        # Find function calls (word followed by parentheses)
        function_pattern = r'\b(\w+)\s*\('
        functions = re.findall(function_pattern, statement, re.IGNORECASE)
        
        function_counts = {}
        for func in functions:
            func_upper = func.upper()
            function_counts[func_upper] = function_counts.get(func_upper, 0) + 1
        
        function_analysis['function_calls'] = function_counts
        function_analysis['total_function_calls'] = sum(function_counts.values())
        function_analysis['unique_functions'] = len(function_counts)
        
        return function_analysis
    
    def _analyze_syntax_patterns(self, error_message: str, statement: str) -> Dict:
        """Analyze syntax patterns generically."""
        syntax_analysis = {}
        
        # Look for common syntax constructs
        syntax_constructs = {
            'parentheses_pairs': statement.count('('),
            'semicolons': statement.count(';'),
            'commas': statement.count(','),
            'quotes': statement.count("'"),
            'double_quotes': statement.count('"'),
            'operators': len(re.findall(r'[=<>!]+', statement))
        }
        
        syntax_analysis['syntax_construct_counts'] = syntax_constructs
        
        return syntax_analysis
    
    def _generate_generic_suggestions(self, analysis: Dict) -> List[str]:
        """Generate generic analysis suggestions based on findings."""
        suggestions = []
        
        # Complexity-based suggestions
        if analysis.get('statement_complexity', {}).get('has_multiple_statements'):
            suggestions.append("Statement contains multiple SQL operations - analyze each separately")
        
        if analysis.get('statement_complexity', {}).get('has_subqueries'):
            suggestions.append("Statement contains subqueries - check each for the same error pattern")
        
        # Pattern-based suggestions
        repeated_keywords = analysis.get('repeated_keywords', {})
        if repeated_keywords:
            most_repeated = max(repeated_keywords.items(), key=lambda x: x[1])
            suggestions.append(f"Keyword '{most_repeated[0]}' appears {most_repeated[1]} times - check for consistent usage")
        
        # Function-based suggestions
        if analysis.get('total_function_calls', 0) > 0:
            suggestions.append("Statement contains function calls - verify PostgreSQL compatibility")
        
        # Structure-based suggestions
        clause_counts = analysis.get('clause_counts', {})
        if len(clause_counts) > 3:
            suggestions.append("Complex statement with multiple clauses - verify PostgreSQL syntax ordering")
        
        return suggestions

def create_generic_analysis_prompt(source_context: Dict, target_error_context: Dict, error_message: str) -> str:
    """
    Create a generic analysis-enhanced prompt without hardcoded scenarios.
    """
    analyzer = GenericErrorAnalyzer()
    analysis = analyzer.analyze_error_generically(error_message, target_error_context.error_statement)
    
    return f"""# Generic Oracle to PostgreSQL Migration Analysis

## 🔍 GENERIC ERROR ANALYSIS
**Error Keywords Detected:** {', '.join(analysis['error_keywords'])}
**Statement Complexity:** {analysis['statement_complexity']['line_count']} lines, {analysis['statement_complexity']['character_count']} characters
**Multiple Statements:** {'Yes' if analysis['statement_complexity']['has_multiple_statements'] else 'No'}
**Subqueries Present:** {'Yes' if analysis['statement_complexity']['has_subqueries'] else 'No'}

## 📊 PATTERN ANALYSIS
**Total SQL Clauses:** {analysis.get('total_clauses', 0)}
**Function Calls:** {analysis.get('total_function_calls', 0)} total, {analysis.get('unique_functions', 0)} unique
**Most Repeated Keyword:** {analysis.get('most_frequent', ['None', 0])[0]} ({analysis.get('most_frequent', ['None', 0])[1]} times)

## 💡 GENERIC ANALYSIS SUGGESTIONS
{chr(10).join([f"- {suggestion}" for suggestion in analysis['analysis_suggestions']])}

## 📊 ERROR DETAILS
**Error Message:** {error_message}

**Error Statement (#{target_error_context.error_statement_number}):**
```sql
{target_error_context.error_statement}
```

## 🎯 SYSTEMATIC APPROACH
1. **Pattern Detection**: Identify what construct is causing the PostgreSQL error
2. **Scope Analysis**: Determine if the same pattern appears multiple times in the statement
3. **Conversion Strategy**: Find the appropriate PostgreSQL equivalent
4. **Comprehensive Application**: Fix ALL instances of the pattern within the statement

## 📤 REQUIRED OUTPUT
```json
{{
  "corrected_statements": [
    {{
      "statement_number": {target_error_context.error_statement_number},
      "original_statement": "<original>",
      "corrected_statement": "<corrected with ALL patterns fixed>",
      "statement_type": "error_statement",
      "changes_made": "<specific changes applied>"
    }}
  ],
  "explanation": "<analysis and comprehensive fix rationale>"
}}
```

## ⚠️ CRITICAL: Apply systematic analysis to identify and fix ALL instances of the problematic pattern.
"""
