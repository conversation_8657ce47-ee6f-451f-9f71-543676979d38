"""
Test script to compare the enhanced prompt approach with the current one.
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from prompts.enhanced_statement_conversion_prompt import create_enhanced_statement_conversion_prompt
from prompts.error_pattern_analyzer import create_pattern_aware_prompt, ErrorPatternAnalyzer

# Mock context objects for testing
class MockContext:
    def __init__(self, stmt_num, statement):
        self.error_statement_number = stmt_num
        self.before_statement_number = stmt_num - 1
        self.after_statement_number = stmt_num + 1
        self.error_statement = statement
        self.before_statement = "-- Before statement context"
        self.after_statement = "-- After statement context"

def test_enhanced_prompts():
    """Test the enhanced prompt approaches."""
    
    # Test case: HAVING/GROUP BY error
    error_message = 'syntax error at or near "GROUP" LINE 215: GROUP BY'
    
    problematic_statement = """
    INSERT INTO TESTREPORTS (PARAMETERID, PARAMETERD<PERSON>AILID, RESULT)
    SELECT DISTINCT tp.parameterid, LLP.PARAMETERDETAILID, eta.param_result_flag
    FROM requesttests RT
    INNER JOIN testmaster TM ON RT.testid = TM.serviceid
    WHERE RT.requesttestid = lv_requesttestid[RESULT_COUNT]
    HAVING MAX(eta.test_transmission_time) = MAX(eta.test_transmission_time)
    GROUP BY tp.parameterid, LLP.PARAMETERDETAILID, eta.param_result_flag;
    
    INSERT INTO TESTREPORTS (PARAMETERID, PARAMETERDETAILID, RESULT)
    SELECT DISTINCT tp.parameterid, LLP.PARAMETERDETAILID, eta.param_result_flag
    FROM requesttests RT
    INNER JOIN testmaster TM ON RT.testid = TM.serviceid
    WHERE RT.requesttestid = lv_requesttestid[RESULT_COUNT]
    HAVING MAX(eta.test_transmission_time) = MAX(eta.test_transmission_time)
    GROUP BY tp.parameterid, LLP.PARAMETERDETAILID, eta.param_result_flag;
    """
    
    source_context = MockContext(24, "-- Oracle source statement")
    target_context = MockContext(30, problematic_statement)
    
    print("=" * 80)
    print("TESTING ENHANCED PROMPT APPROACHES")
    print("=" * 80)
    
    # Test 1: Enhanced Statement Conversion Prompt
    print("\n1. ENHANCED STATEMENT CONVERSION PROMPT:")
    print("-" * 50)
    enhanced_prompt = create_enhanced_statement_conversion_prompt(
        source_context, target_context, error_message
    )
    print(f"Length: {len(enhanced_prompt)} characters")
    print("Key features: Structured, focused, systematic")
    
    # Test 2: Pattern-Aware Prompt
    print("\n2. PATTERN-AWARE PROMPT:")
    print("-" * 50)
    pattern_prompt = create_pattern_aware_prompt(
        source_context, target_context, error_message
    )
    print(f"Length: {len(pattern_prompt)} characters")
    print("Key features: Error pattern analysis, instance counting")
    
    # Test 3: Error Pattern Analysis
    print("\n3. ERROR PATTERN ANALYSIS:")
    print("-" * 50)
    analyzer = ErrorPatternAnalyzer()
    analysis = analyzer.analyze_error(error_message, problematic_statement)
    
    print(f"Error Type: {analysis['error_type']}")
    print(f"Pattern Count: {analysis['pattern_count']}")
    print(f"Guidance: {analysis['specific_guidance']}")
    print(f"Solution Strategy: {analysis['solution_strategy']}")
    
    # Test 4: Compare with current approach
    print("\n4. COMPARISON WITH CURRENT APPROACH:")
    print("-" * 50)
    
    # Load current prompt for comparison
    try:
        from prompts.statement_conversion_prompt import create_statement_conversion_prompt
        current_prompt = create_statement_conversion_prompt(
            source_context, target_context, error_message
        )
        print(f"Current prompt length: {len(current_prompt)} characters")
        print(f"Enhanced prompt length: {len(enhanced_prompt)} characters")
        print(f"Reduction: {len(current_prompt) - len(enhanced_prompt)} characters ({((len(current_prompt) - len(enhanced_prompt)) / len(current_prompt) * 100):.1f}% shorter)")
    except Exception as e:
        print(f"Could not load current prompt: {e}")
    
    print("\n5. RECOMMENDATIONS:")
    print("-" * 50)
    print("✅ Use Enhanced Prompt for: Clear, focused instructions")
    print("✅ Use Pattern-Aware Prompt for: Complex error patterns")
    print("✅ Use Error Analyzer for: Systematic error classification")
    print("✅ Benefits: Shorter, more focused, pattern-specific guidance")

if __name__ == "__main__":
    test_enhanced_prompts()
