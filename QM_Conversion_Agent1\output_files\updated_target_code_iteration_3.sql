set search_path to LAB;
create or replace function LAB.F_GETTESTSTATUS_T(in_RequestTestId IN LAB.REQUESTTESTS.REQUESTTESTID%TYPE)
returns varchar
language plpgsql
security definer
as $$
declare
v_TestStatus varchar(50) := null;
v_StorageRequired LAB.REQUESTTESTS.STORAGEREQUIRED%TYPE;
v_SendToExternalLab LAB.REQUESTTESTS.SENDTOEXTERNALLAB%TYPE;
v_SendWithinApollo LAB.REQUESTTESTS.SENDWITHINAPOLLO%TYPE;
v_Processing LAB.REQUESTTESTS.PROCESSING%TYPE;
v_SampleCollected LAB.REQUESTTESTS.SAMPLECOLLECTED%TYPE;
v_PrepareSample LAB.REQUESTTESTS.PREPARESAMPLE%TYPE;
v_SampleVerify LAB.REQUESTTESTS.SAMPLEVERIFY%TYPE;
v_ReportDispatched LAB.REQUESTTESTS.Printasoriginal%TYPE;
v_Cancelstatus LAB.REQUESTTESTS.Teststatus%TYPE;
v_DepartmentID numeric;
--V_Count Number;


begin
set search_path to LAB;
SELECT RT.DEPARTMENTID
into strict v_DepartmentID
FROM LAB.REQUESTTESTS RT
WHERE RT.REQUESTTESTID = in_RequestTestId;
SELECT RT.STORAGEREQUIRED,
RT.SENDTOEXTERNALLAB,
RT.SENDWITHINAPOLLO,
RT.PROCESSING,
RT.SAMPLECOLLECTED,
RT.PREPARESAMPLE,
RT.SAMPLEVERIFY,
RT.Printasoriginal,
RT.Teststatus
into strict v_StorageRequired,
v_SendToExternalLab,
v_SendWithinApollo,
v_Processing,
v_SampleCollected,
v_PrepareSample,
v_SampleVerify,
v_ReportDispatched,
v_Cancelstatus
FROM LAB.REQUESTTESTS RT
WHERE REQUESTTESTID = in_RequestTestId;
CASE WHEN v_Cancelstatus = 0 THEN
v_TestStatus := 'Cancelled';
WHEN v_ReportDispatched = 'R' and v_SampleVerify = 'Y' THEN
v_TestStatus := 'DispatchedResultsVerified';
WHEN v_ReportDispatched = 'R' and v_SampleVerify = 'P' THEN
v_TestStatus := 'DispatchedResultsReported';
WHEN v_ReportDispatched = 'R' and v_SampleVerify = 'E' THEN
v_TestStatus := 'DispatchedResultsReReported';
WHEN v_SampleVerify = 'E' THEN
v_TestStatus := 'ResultsReReported';
WHEN v_ReportDispatched = 'D' or v_ReportDispatched = 'A' THEN
v_TestStatus := 'ReportDispatched';
WHEN v_ReportDispatched = 'P' or v_ReportDispatched = 'S' THEN
v_TestStatus := 'ReportPrinted';
WHEN v_SampleVerify = 'Y' THEN
v_TestStatus := 'ResultsVerified';
WHEN v_SampleVerify = 'P' THEN
v_TestStatus := 'ResultsReported';
WHEN v_Processing = 'Y' THEN
v_TestStatus := 'ProcessingCompleted';
WHEN v_PrepareSample = 'Y' THEN
v_TestStatus := 'SampleReceived';
WHEN v_SampleCollected = 'Y' THEN
v_TestStatus := 'SampleCollected';
ELSE
v_TestStatus := 'RequestRaised';
-- END CASE
end;
RETURN 'ReportDispatched';
--RETURN v_TestStatus;


EXCEPTION
WHEN OTHERS THEN
RETURN NULL;
end;
RETURN 'ReportDispatched';