"""
Error pattern analyzer for database migration.
Provides structured analysis of common Oracle to PostgreSQL migration errors.
"""
import re
from typing import Dict, List, Tuple

class ErrorPatternAnalyzer:
    """
    Analyzes error patterns and provides structured guidance for fixes.
    Inspired by <PERSON><PERSON><PERSON><PERSON>'s systematic approach to SQL analysis.
    """
    
    def __init__(self):
        self.error_patterns = {
            'syntax_order': {
                'patterns': [
                    r'syntax error at or near "GROUP"',
                    r'HAVING.*GROUP BY',
                    r'ORDER BY.*GROUP BY'
                ],
                'description': 'SQL clause ordering issue',
                'solution_type': 'reorder_clauses'
            },
            'function_compatibility': {
                'patterns': [
                    r'function.*does not exist',
                    r'operator does not exist',
                    r'type.*does not exist'
                ],
                'description': 'Oracle function not supported in PostgreSQL',
                'solution_type': 'function_replacement'
            },
            'data_type_mismatch': {
                'patterns': [
                    r'cannot cast type',
                    r'invalid input syntax for type',
                    r'operator.*for type'
                ],
                'description': 'Data type compatibility issue',
                'solution_type': 'type_conversion'
            },
            'plpgsql_syntax': {
                'patterns': [
                    r'syntax error at or near "GET"',
                    r'GET DIAGNOSTICS',
                    r'row_count'
                ],
                'description': 'PL/pgSQL syntax issue',
                'solution_type': 'plpgsql_fix'
            }
        }
    
    def analyze_error(self, error_message: str, statement: str) -> Dict:
        """
        Analyze the error and provide structured guidance.
        
        Args:
            error_message: The PostgreSQL error message
            statement: The SQL statement causing the error
            
        Returns:
            Dictionary with error analysis and solution guidance
        """
        analysis = {
            'error_type': 'unknown',
            'pattern_matches': [],
            'solution_strategy': 'generic_fix',
            'specific_guidance': '',
            'pattern_count': 0,
            'pattern_locations': []
        }
        
        # Analyze error message against known patterns
        for error_type, config in self.error_patterns.items():
            for pattern in config['patterns']:
                if re.search(pattern, error_message, re.IGNORECASE):
                    analysis['error_type'] = error_type
                    analysis['pattern_matches'].append(pattern)
                    analysis['solution_strategy'] = config['solution_type']
                    analysis['specific_guidance'] = config['description']
                    break
            if analysis['error_type'] != 'unknown':
                break
        
        # Count pattern occurrences in statement
        if analysis['error_type'] == 'syntax_order':
            analysis.update(self._analyze_syntax_order(statement))
        elif analysis['error_type'] == 'function_compatibility':
            analysis.update(self._analyze_function_compatibility(statement))
        elif analysis['error_type'] == 'plpgsql_syntax':
            analysis.update(self._analyze_plpgsql_syntax(statement))
        
        return analysis
    
    def _analyze_syntax_order(self, statement: str) -> Dict:
        """Analyze syntax ordering issues, particularly HAVING/GROUP BY."""
        having_pattern = r'HAVING\s+[^;]*?GROUP\s+BY'
        matches = list(re.finditer(having_pattern, statement, re.IGNORECASE | re.DOTALL))
        
        return {
            'pattern_count': len(matches),
            'pattern_locations': [(m.start(), m.end()) for m in matches],
            'specific_guidance': f'Found {len(matches)} instances of HAVING before GROUP BY. All must be reordered.'
        }
    
    def _analyze_function_compatibility(self, statement: str) -> Dict:
        """Analyze function compatibility issues."""
        oracle_functions = ['SYSDATE', 'NVL', 'DECODE', 'ROWNUM', 'DUAL']
        found_functions = []
        
        for func in oracle_functions:
            if re.search(rf'\b{func}\b', statement, re.IGNORECASE):
                found_functions.append(func)
        
        return {
            'pattern_count': len(found_functions),
            'pattern_locations': found_functions,
            'specific_guidance': f'Oracle functions found: {", ".join(found_functions)}. Need PostgreSQL equivalents.'
        }
    
    def _analyze_plpgsql_syntax(self, statement: str) -> Dict:
        """Analyze PL/pgSQL syntax issues."""
        plpgsql_issues = [
            r'GET\s+DIAGNOSTICS.*row_count',
            r'ROW_COUNT',
            r'SQL%ROWCOUNT'
        ]
        
        matches = []
        for pattern in plpgsql_issues:
            matches.extend(list(re.finditer(pattern, statement, re.IGNORECASE)))
        
        return {
            'pattern_count': len(matches),
            'pattern_locations': [(m.start(), m.end()) for m in matches],
            'specific_guidance': f'Found {len(matches)} PL/pgSQL syntax issues. Need proper PostgreSQL syntax.'
        }
    
    def get_solution_template(self, error_type: str) -> str:
        """Get solution template for specific error type."""
        templates = {
            'syntax_order': """
## SYNTAX ORDER FIX
1. Identify ALL instances of incorrect clause ordering
2. Reorder clauses to: WHERE → GROUP BY → HAVING → ORDER BY
3. Ensure ALL occurrences are fixed within the statement
""",
            'function_replacement': """
## FUNCTION REPLACEMENT FIX
1. Identify Oracle-specific functions
2. Map to PostgreSQL equivalents:
   - SYSDATE → CURRENT_TIMESTAMP
   - NVL → COALESCE
   - DECODE → CASE WHEN
3. Replace ALL instances consistently
""",
            'plpgsql_fix': """
## PL/pgSQL SYNTAX FIX
1. Identify PL/pgSQL syntax issues
2. Apply correct PostgreSQL syntax:
   - GET DIAGNOSTICS var = ROW_COUNT
   - Use proper variable declarations
3. Fix ALL instances in the statement
"""
        }
        
        return templates.get(error_type, "## GENERIC FIX\nApply PostgreSQL syntax rules systematically.")

def create_pattern_aware_prompt(source_context: Dict, target_error_context: Dict, error_message: str) -> str:
    """
    Create a pattern-aware prompt using error analysis.
    """
    analyzer = ErrorPatternAnalyzer()
    analysis = analyzer.analyze_error(error_message, target_error_context.error_statement)
    
    return f"""# Pattern-Aware PostgreSQL Error Resolution

## 🔍 ERROR PATTERN ANALYSIS
**Error Type:** {analysis['error_type']}
**Pattern Count:** {analysis['pattern_count']} instances found
**Guidance:** {analysis['specific_guidance']}

{analyzer.get_solution_template(analysis['error_type'])}

## 📊 ERROR DETAILS
**Error Message:** {error_message}

**Error Statement (#{target_error_context.error_statement_number}):**
```sql
{target_error_context.error_statement}
```

## 🎯 SYSTEMATIC RESOLUTION
1. **Pattern Recognition**: {analysis['error_type']} detected
2. **Instance Count**: Fix ALL {analysis['pattern_count']} occurrences
3. **Solution Strategy**: {analysis['solution_strategy']}
4. **Comprehensive Fix**: Ensure no instances remain unfixed

## 📤 REQUIRED OUTPUT
```json
{{
  "corrected_statements": [
    {{
      "statement_number": {target_error_context.error_statement_number},
      "original_statement": "<original>",
      "corrected_statement": "<corrected with ALL patterns fixed>",
      "statement_type": "error_statement",
      "changes_made": "<specific pattern fixes applied>"
    }}
  ],
  "explanation": "<pattern analysis and comprehensive fix rationale>"
}}
```

## ⚠️ CRITICAL: Fix ALL {analysis['pattern_count']} instances of the pattern within the statement.
"""
