"""
Enhanced statement conversion prompt inspired by Lang<PERSON>hain SQL Database Toolkit approach.
Focused, structured, and database-specific.
"""
from typing import Dict

def create_enhanced_statement_conversion_prompt(source_context: Dict, target_error_context: Dict, error_message: str, target_statements: list = None, previous_feedback: str = None) -> str:
    """
    Creates a focused, structured prompt for statement conversion using database-specific expertise.
    
    This approach is inspired by <PERSON><PERSON><PERSON><PERSON>'s SQL Database Toolkit methodology:
    - Structured error analysis
    - Database-specific pattern recognition
    - Systematic solution application
    - Clear, focused instructions
    """
    
    # Check if this is a target database-specific scenario
    is_target_specific = (source_context.error_statement_number == 0 or
                         not source_context.error_statement or
                         source_context.error_statement.strip() == "")
    
    # Add feedback section if provided
    feedback_section = ""
    if previous_feedback:
        feedback_section = f"""
🔄 PREVIOUS ATTEMPT FEEDBACK:
{previous_feedback}

📋 REQUIRED IMPROVEMENTS:
- Address the specific issues mentioned above
- Apply more thorough pattern analysis
- Ensure complete error resolution
"""

    # Core database expertise prompt
    if is_target_specific:
        return f"""# PostgreSQL Database Expert - Error Resolution

## 🎯 OBJECTIVE
Fix the PostgreSQL syntax error using database-specific expertise.

{feedback_section}

## 📊 ERROR ANALYSIS
**Error Message:** {error_message}

**Error Statement (#{target_error_context.error_statement_number}):**
```sql
{target_error_context.error_statement}
```

**Context Before (#{target_error_context.before_statement_number}):**
```sql
{target_error_context.before_statement}
```

**Context After (#{target_error_context.after_statement_number}):**
```sql
{target_error_context.after_statement}
```

## 🔍 SYSTEMATIC ERROR RESOLUTION

### Step 1: Error Pattern Identification
- Identify the specific PostgreSQL syntax rule being violated
- Determine the exact location and nature of the error
- Classify the error type (syntax, semantic, data type, etc.)

### Step 2: Comprehensive Pattern Scanning
- **CRITICAL**: Scan the ENTIRE error statement for ALL instances of the same pattern
- Count total occurrences of the problematic pattern
- Map each occurrence location within the statement

### Step 3: PostgreSQL-Specific Solution
- Apply PostgreSQL syntax rules and best practices
- Use database-native constructs and patterns
- Ensure semantic correctness and functionality preservation

### Step 4: Complete Pattern Resolution
- Fix ALL instances of the identified pattern within the statement
- Verify no partial fixes remain
- Ensure comprehensive error resolution

## 🛠️ SOLUTION REQUIREMENTS
1. **Complete Analysis**: Identify ALL occurrences of the error pattern
2. **Comprehensive Fix**: Resolve EVERY instance within the statement
3. **PostgreSQL Compliance**: Use proper PostgreSQL syntax and semantics
4. **Functionality Preservation**: Maintain original business logic
5. **Executable Code**: Provide working PostgreSQL statements only

## 📤 OUTPUT FORMAT
```json
{{
  "corrected_statements": [
    {{
      "statement_number": {target_error_context.error_statement_number},
      "original_statement": "<original statement>",
      "corrected_statement": "<corrected PostgreSQL statement>",
      "statement_type": "error_statement",
      "changes_made": "<specific changes applied>"
    }}
  ],
  "explanation": "<systematic analysis and solution rationale>"
}}
```

## ⚠️ CRITICAL RULES
- Fix ONLY the error statement (#{target_error_context.error_statement_number})
- Scan for ALL instances of the same error pattern
- Apply PostgreSQL expertise systematically
- Provide executable code, not comments or placeholders
"""

    else:
        return f"""# Oracle to PostgreSQL Migration Expert - Error Resolution

## 🎯 OBJECTIVE
Convert the Oracle statement to correct PostgreSQL syntax, resolving the deployment error.

{feedback_section}

## 📊 ERROR ANALYSIS
**Error Message:** {error_message}

**Oracle Source (#{source_context.error_statement_number}):**
```sql
{source_context.error_statement}
```

**PostgreSQL Target with Error (#{target_error_context.error_statement_number}):**
```sql
{target_error_context.error_statement}
```

**Context Before (#{target_error_context.before_statement_number}):**
```sql
{target_error_context.before_statement}
```

**Context After (#{target_error_context.after_statement_number}):**
```sql
{target_error_context.after_statement}
```

## 🔍 SYSTEMATIC MIGRATION ANALYSIS

### Step 1: Error Pattern Identification
- Identify the specific PostgreSQL syntax rule being violated
- Compare with Oracle source to understand intended functionality
- Classify the migration challenge (syntax, function, data type, etc.)

### Step 2: Comprehensive Pattern Scanning
- **CRITICAL**: Scan the ENTIRE error statement for ALL instances of the same pattern
- Count total occurrences of the problematic pattern
- Map each occurrence location within the statement
- Identify if multiple sub-queries or nested statements contain the same issue

### Step 3: Oracle-to-PostgreSQL Conversion Strategy
- Analyze Oracle-specific constructs causing the error
- Research PostgreSQL equivalents for the same functionality
- Design conversion approach that preserves business logic

### Step 4: Complete Pattern Resolution
- Convert ALL instances of the identified pattern within the statement
- Apply consistent PostgreSQL syntax across all occurrences
- Verify no partial conversions remain

## 🛠️ MIGRATION EXPERTISE

### Common Oracle → PostgreSQL Patterns:
- **Syntax Order**: PostgreSQL requires specific clause ordering (GROUP BY before HAVING)
- **Data Types**: Oracle NUMBER → PostgreSQL NUMERIC, VARCHAR2 → VARCHAR
- **Functions**: Oracle SYSDATE → PostgreSQL CURRENT_TIMESTAMP
- **Operators**: Oracle || → PostgreSQL CONCAT or ||
- **Control Flow**: Oracle PL/SQL → PostgreSQL PL/pgSQL syntax differences

### Error-Specific Solutions:
- **GROUP BY Errors**: Ensure proper clause ordering and column references
- **Function Errors**: Replace Oracle functions with PostgreSQL equivalents
- **Data Type Errors**: Apply proper casting and type conversion
- **Syntax Errors**: Rewrite using PostgreSQL-compliant syntax

## 📤 OUTPUT FORMAT
```json
{{
  "corrected_statements": [
    {{
      "statement_number": {target_error_context.error_statement_number},
      "original_statement": "<original PostgreSQL statement>",
      "corrected_statement": "<corrected PostgreSQL statement>",
      "statement_type": "error_statement",
      "changes_made": "<specific Oracle→PostgreSQL conversions applied>"
    }}
  ],
  "explanation": "<systematic migration analysis and conversion rationale>"
}}
```

## ⚠️ CRITICAL RULES
- Fix ONLY the error statement (#{target_error_context.error_statement_number})
- Scan for ALL instances of the same error pattern within the statement
- Apply Oracle→PostgreSQL migration expertise systematically
- Preserve original business logic and functionality
- Provide executable PostgreSQL code, not comments or placeholders
- Handle multiple sub-queries or nested statements comprehensively
"""
